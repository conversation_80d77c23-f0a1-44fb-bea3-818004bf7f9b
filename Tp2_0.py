# -*- coding: utf-8 -*-
"""
Created on Sat Apr  5 12:26:46 2025

@author: sabri lyazid
"""
 
# import sys 
# for p in sys.path: print (p)

import nltk  # you have to install the package  pip install nltk
text=['IoT devices bring convenience but often at the cost of privacy. Many collect sensitive data like location or health stats without clear user consent. As these devices grow in number, so do concerns about data security and surveillance.',
      'The Internet of Things (IoT) refers to the network of interconnected devices embedded with sensors, software, and other technologies to exchange data over the internet.  ',
      'Patients are often unaware that their medical records can be accessed by third-party services, creating a risk of data misuse and identity theft',
      'You must perform regular exercise, a   diet, and adequate sleep. They are key factors in maintaining overall physical and mental health',    
      'High blood pressure is often called the "silent killer" because it has no obvious symptoms',
      'Autonomous systems in smart homes can control when and how data is shared, maintaining user privacy'
      'Robots powered by intelligent systems can autonomously perform tasks in manufacturing, reducing the need for human intervention'
      'Intelligent systems in healthcare can analyze patient data to predict potential health risks']
#convert list to data frame
import pandas as pd 
df = pd.DataFrame({'myTexts':text})
#affiche le contenu de toute la cellule
pd.set_option('display.max_colwidth', None)
print(df)
 
#Perform lowercasing on a data frame, use the apply a function  
df['myTexts'] = df['myTexts'].apply(lambda x: " ".join(x.lower() for x in x.split()))
df['myTexts'] 


#     remove punctuation
#Removing punctuation from strings in Python can be handled efficiently through various methods, 
#each suited to different needs and contexts.

import string
string.punctuation

def remove_punctuation(input_string):
    result = input_string
    for char in string.punctuation:
        result = result.replace(char, '')
    return result

text_1="Les fleurs, de la terasse, sont magnifiques"
remove_punctuation(text_1)
text_1="I love cooking, my wife, and my son" # the meaning will be different when we drop punctuation
remove_punctuation(text_1)

# autre méthode
import re 
 
result_text_1=re.sub(f"[{re.escape(string.punctuation)}]","", text_1)
result_text_1
 
 
pattern = r'[^\w\s]'  # ne supprimer pas les chiffres et _ (underscore)
df['myTexts'] =df['myTexts'].str.replace(pattern,'',regex=True)
df['myTexts'] 

# la méthode isalnum garde les charactères alphanumérique, pour retirer également les chiffres utilser isalpha
df['myTexts'] = df['myTexts'].apply(lambda x: ''.join(char for char in x if char.isalnum() or char.isspace()))
df['myTexts'] = df['myTexts'].apply(lambda x: ''.join(char for char in x if char.isalpha() or char.isspace())) 
df['myTexts'] 

#!pip install nltk
#Removing Stop Words
#use of NLTK library,
import nltk  # you should install this library  PIP INSTALL NLTK 
#C:\Users\<USER>\AppData\Roaming\nltk_data    the sownloaded  
#https://raw.githubusercontent.com/nltk/nltk_data/gh-pages/index.xml  the server index

nltk.download()
from nltk.corpus import stopwords
stop = stopwords.words('english') 
print(stop)
#remarquer que la liste ne contient de nombreux termes de plus il faut que 
# votre textes soit en miniscule sinon les termes ne seront pas tous supprimés
df['myTexts'] = df['myTexts'].apply(lambda x: " ".join(x for x in x.split() if x not in stop))
df['myTexts']

 ######################## Standardizing Text
# remarquer ici que nlp n'est pas supprimer et donc si on créé un dictionnaire de  
# mot courts etc. on peut supprimer ce qui nous interesse pas 
#par exemple: sth: some things , nlp :natural language processing', tbh: To be honest
# lol: Laugh out loud, aka: Also known as (a.k.a.), asap: As soon as possible
 

 # Example DataFrame
data = {'Text': ['a.k.a. This is introduction to NLP','It lol  is likely to be useful, to people  ',
                 'Machine learning asap is the new electrcity',
                 'There would be less hype around AI and more action going  forward',   
                 'python is the best tool!','R is good langauage','I like this book',
                 'I want more books like this']}

df = pd.DataFrame(data)
df
  
df= df.map(lambda x: x.lower() if isinstance(x, str) else x)
df
# Create a dictionary for replacing sentences

replace_dict = {
     'nlp': 'natural language processing', 
     'lol':'laugh out loud',
     'asap': 'As soon as possible',
     'a.k.a.' : 'also known as',
}
  

 # Replace sentences
for old_sentence, new_sentence in replace_dict.items():
     df['Text'] = df['Text'].str.replace(old_sentence, new_sentence)

print(df)


############ Spelling correction
#Install textblob library   !pip install textblob
#import libraries and use 'correct' function
from textblob import TextBlob
text=['This is intrductionn to NLP','It is likelly to be usefule, to peopple  ','Machin larning is the new electrcty','There would be less hype aroundee AI and morre action going  forwarder',      'Python is the bests tool!','R is goode langaage','I like thiss book','I want more books like this']



#convert list to data frame   #Python not recognized 
import pandas as pd
df = pd.DataFrame({'myTexts':text})
print(df)
df['myTexts'].apply(lambda x: str(TextBlob(x).correct()))


# use autocorrect library as shown below
#install autocorrect !pip install autocorrect 
from autocorrect import Speller
spell = Speller()
spell("I'm not sleapy and tehre is no place I'm giong to.") 
spell.get_candidates("heare")
spell.get_candidates("thesi")
spell.get_candidates("ltie")


 #################################"" Tokenizing Text
 #Using textblob   ou nltk
from textblob import TextBlob 
nltk.word_tokenize( )

from nltk.tokenize import word_tokenize 
nltk.download('punkt')

# Sample DataFrame with a text column
data = {'text': ['This is intrduction to NLP','It is likely to be useful, to peopple  ','Machine learning is the new electrcity','There would be less hype arounde AI and more action going  forward',      'Python is the bests tool!','R is goode language','I like this book','I want more books like this']}
df = pd.DataFrame(data)

# Tokenizing the text column
df['tokens'] = df['text'].apply(lambda x: word_tokenize(x))


df['tokens'] = df['myTexts'].apply(lambda x: word_tokenize(x))
print(df['tokens'])


##############   TextBlob

from textblob import TextBlob
df['tokens'] = df['myTexts'].apply(lambda x: TextBlob(x).words)

df['tokens'] 
###########################   Stemimng  using NLTK or a TextBlob library.

 
from nltk.stem import PorterStemmer
from nltk.tokenize import word_tokenize
stemmer = PorterStemmer()
df['myTexts']


df['myTexts'][:5].apply(lambda x: " ".join([stemmer.stem(word) for word in x.split()]))


# use another method 
# Function to tokenize and stem text


def stemming_text(text):
    tokens = word_tokenize(text)  # Tokenizing the text
    return [stemmer.stem(word) for word in tokens]  # Stemming the tokens

df['stem_tokens'] = df['myTexts'].apply(stemming_text)

df['stem_tokens']


texts = [
    "running quickly",
    "swimming is fun",
    "cats are playing",
    "dogs running fast",
    "happily running and jumping",
    "I like fishing",
    "I eat fish",
    "leaves",
    
]

text_to_stem = [stemming_text(text) for text in texts]
for startToken, stemmed in zip(texts, text_to_stem):
    print(f"Original: {startToken}\nStemmed: {stemmed}\n")


#####################  Lematization  NLTK , spaCy or the TextBlob library.
from textblob import Word
data = {'text': ['This is intrduction to NLP','It is likely to be useful, to people  ','Machine learning is the new electrcity','There would be less hype arounde AI and more action going  forward',      'Python is the bests tool!','R is good language','I like this book','I want more books like this']}
df = pd.DataFrame(data)
df
#df['text'] = df['text'].apply(lambda x: " ".join([Word(word).lemmatize() for word in x.split()]))
#df['text'] 
# using nltk 
import pandas as pd
import nltk
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
nltk.download('punkt')
nltk.download('wordnet')
lemmatizer = WordNetLemmatizer()

def lemmatize_text_nltk(text):
    tokens = word_tokenize(text)  # Tokenize the text
    return [lemmatizer.lemmatize(word) for word in tokens]  # Lemmatize each token

# Apply lemmatization to the text column
df['lemmatized_tokens'] = df['text'].apply(lemmatize_text_nltk)
df['lemmatized_tokens']
print(df)

import nltk
from nltk.stem import WordNetLemmatizer

# Download the necessary NLTK data files
nltk.download('wordnet')
nltk.download('omw-1.4')

# Initialize the Lemmatizer
lemmatizer = WordNetLemmatizer()

# Example words
words = ["running", "ran", "better", "flies"]

# Lemmatize each word
for word in words:
    lemmatized_word = lemmatizer.lemmatize(word, pos='v')  # pos='v' is for verbs
print(f"{word} -> {lemmatized_word}")
print(f"{word} -> {lemmatized_word}")


import pandas as pd
import nltk
from nltk.stem import WordNetLemmatizer

# Download necessary NLTK data
nltk.download('wordnet')
nltk.download('omw-1.4')
nltk.download('punkt')

# Create a sample DataFrame
data = {'text': ['running fast', 'I eat fish',  "leaves", 'flies high', 'children built games', 'ran yesterday', 'are better performance']}
df = pd.DataFrame(data)
df
# Initialize the Lemmatizer
lemmatizer = WordNetLemmatizer()

# Define a function to lemmatize words in a sentence
def lemmatize_text(text):
    words = nltk.word_tokenize(text)  # Tokenize the sentence into words
    lemmatized_words = [lemmatizer.lemmatize(word, pos='a') for word in words]  # Lemmatize each word
    return ' '.join(lemmatized_words)  # Join the lemmatized words back into a sentence

# We can substitute pos='v' with pos='n' for noun, or a for adjective 

# Apply the lemmatization function to the 'Text' column
df['Lemmatized_Text'] = df['myTexts'].apply(lemmatize_text)

# Print the resulting DataFrame
print(df)


 




# using spacy  
# from the consol import : python -m spacy download en_core_web_sm
import pandas as pd
import spacy
nlp = spacy.load('en_core_web_sm')
def lemmatize_text(text):
    doc = nlp(text)  # Process the text with spaCy
    return [token.lemma_ for token in doc]  # Extract the lemmatized tokens
df['text_lem'] = df['myTexts'].apply(lemmatize_text)
df['text_lem']


# explore data 
def count_words(text):
    return len(text.split())
df['words_count'] = df['myTexts'].apply(count_words)
df['words_count'] 


# frequency of the word 
from nltk.probability import FreqDist
data = {'text': ['This is intrduction to NLP','It is likely to be useful, to people  ','Machine learning is the new electrcity','There would be less hype arounde AI and more action going  forward',      'Python is the bests tool!','R is good language','I like this book','I want more books like this']}
df = pd.DataFrame(data)
df

#or use our textes
df = pd.DataFrame({'myTexts':text})
df


# Tokenize all words in the 'text' column
wt_words = [word for text in df['myTexts'] for word in nltk.word_tokenize(text)]
fdist = FreqDist(wt_words)
print(fdist)
# Display the 5 most common words and their frequencies
print(fdist.most_common(5))
 
# take the words only if their frequency is greater than 2.
import matplotlib.pyplot as plt
frequency_dist = nltk.FreqDist(wt_words)
frequency_dist
large_words = dict([(k,v) for k,v in frequency_dist.items() if len(k)>2])
frequency_dist.plot(50,cumulative=False)


# World cloud  install from consol : pip install wordcloud
from wordcloud import WordCloud
wcloud = WordCloud().generate_from_frequencies(frequency_dist)
plt.imshow(wcloud, interpolation='bilinear')
plt.axis("off")
(-0.5, 450.5, 202.5, -0.5)
plt.show()

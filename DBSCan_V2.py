# -*- coding: utf-8 -*-
"""
Created on Sun Apr 13 14:59:54 2025

@author: person
"""

import pandas as pd
 
import matplotlib.pyplot as plt

# Sample DataFrame
data1 = {
    'texte1': [3,5, 8, 3, 4, 4, 6,7,5,8,8,7,9],
    'text2': [1,7, 4, 3, 4, 7, 7,0,5,2,5,5,3]
}

data2 = {
    'texte1': [3,5,7,7,3,3,4,6,6,8,2,2],
    'text2' : [7,5,3,2,3,6,6,4,2,4,6,5]
}

df = pd.DataFrame(data2)
df 
# Tracer le graphique
df.plot(x='texte1', y='text2', kind='scatter')  # kind='bar', 'line', 'scatter', etc.
plt.title('Trace tokens')
plt.xlabel('text1')
plt.ylabel('text2')
plt.show()

# afficher matrice de distance 
from scipy.spatial.distance import cdist
 
distances = cdist(df, df, metric='euclidean')

distance_matrix = pd.DataFrame(
    distances,
    columns=[f'Point {i}' for i in range(len(df))],
    index=[f'Point {i}' for i in range(len(df))]
)
pd.set_option('display.max_columns', None)
print(distance_matrix.round(2))

 
 

from sklearn.cluster import KMeans
# Appliquer KMeans
k=5
kmeans = KMeans(n_clusters=k, random_state=0)
df['cluster'] = kmeans.fit_predict(df[['texte1', 'text2']])
  #nombre de clusters
# Afficher les clusters
centers = kmeans.cluster_centers_
plt.scatter(df['texte1'], df['text2'], c=df['cluster'], cmap='viridis', s=100, label='Points')
plt.scatter(centers[:, 0], centers[:, 1], c='red', s=300, marker='X', label='Centroïdes')
plt.title(f'KMeans Clustering (k={k}) avec centroïdes')
plt.xlabel('texte1')
plt.ylabel('text2')
 
plt.grid(True)
plt.show()
print(centers) 
#determiner le k METHOD ELBOW
#Calculer l'inertie pour différents k
inerties = []
K_range = range(2, 5)
for k in K_range:
    kmeans = KMeans(n_clusters=k, random_state=0)
    kmeans.fit(df[['texte1', 'texte1']])
    inerties.append(kmeans.inertia_)  # somme des distances au centroïde
print(inerties)
#èplt.figure(figsize=(8, 6))
plt.plot(K_range, inerties, marker='o')
plt.title('Méthode ELBOW pour trouver le meilleur k')
plt.xlabel('Nombre de clusters (k)')
plt.ylabel('Inertie')
plt.grid(True)
plt.show()


from sklearn.cluster import DBSCAN
df = pd.DataFrame(data2)
df 
#modifier les valeurs de eps et min_sample pour voir les différents effets
dbscan = DBSCAN(eps=1.7, min_samples=5)
labels = dbscan.fit_predict(df)
df['cluster'] = labels
print(df)
plt.scatter(df['texte1'], df['text2'], c=df['cluster'], cmap='rainbow')
plt.title("DBSCAN Clustering")
plt.xlabel("texte1")
plt.ylabel("text2")
plt.show()
#eps et min d'échantillon determine le nombre de cluster 
#si on augmente eps alors la distance (le pas) entre deux points est grande
#si le nombre d'invididus dans un cluster est grand il se peut que 
#eps n'a aucun effet, mais si le nombre d'invidus est petit donc on a plus de chance 
#d'avoir un cluster


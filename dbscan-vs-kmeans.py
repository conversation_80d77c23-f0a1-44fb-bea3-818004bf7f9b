# -*- coding: utf-8 -*-
"""
Created on Sun Apr 13 16:09:44 2025

@author: person
"""

import matplotlib.pyplot as plt
import numpy as np
from sklearn.datasets import make_moons
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler

# Générer des données en 2D (formes non linéaires)
X, _ = make_moons(n_samples=120, noise=0.1, random_state=42)
X = StandardScaler().fit_transform(X)

# Appliquer KMeans
kmeans = KMeans(n_clusters=8, random_state=0)
labels_kmeans = kmeans.fit_predict(X)

# Appliquer DBSCAN
dbscan = DBSCAN(eps=0.3, min_samples=4)
labels_dbscan = dbscan.fit_predict(X)

# Création de la figure
fig, axs = plt.subplots(1, 2, figsize=(12, 5))

# KMeans
axs[0].scatter(X[:, 0], X[:, 1], c=labels_kmeans, cmap='viridis')
axs[0].set_title("KMeans Clustering")
axs[0].grid(True)

# DBSCAN
axs[1].scatter(X[:, 0], X[:, 1], c=labels_dbscan, cmap='viridis')
axs[1].set_title("DBSCAN Clustering")
axs[1].grid(True)

plt.suptitle("Comparaison visuelle : KMeans vs DBSCAN", fontsize=14)
plt.show()

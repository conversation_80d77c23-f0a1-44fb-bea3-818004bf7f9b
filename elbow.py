# -*- coding: utf-8 -*-
"""
Created on Sun Apr 13 15:43:47 2025

@author: person
"""
# set OMP_NUM_THREADS=1
import os
os.environ["OMP_NUM_THREADS"] = "1"
import pandas as pd
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt 
# Données d'exemple
data = {
    'x': [3,5, 8, 3, 4, 4, 6,7,5,8,8,7,9],
    'y': [1,7, 4, 3, 4, 7, 7,0,5,2,5,5,3]
}
data
df = pd.DataFrame(data)
df.plot(x='x', y='y', kind='scatter')  # kind='bar', 'line', 'scatter', etc.
plt.title('Trace tokens')
plt.xlabel('x')
plt.ylabel('y')
plt.show()
# Calculer l'inertie pour différents k
 
inertia = []# voir sur le site skiti learn pour comprendre que inertia_ est
# la somme des distances des individus dans leur clusters
K_range = range(1, 6)
for k in K_range:
    kmeans = KMeans(n_clusters=k, random_state=0)
    kmeans.fit(df[['x', 'y']])
    inertia.append(kmeans.inertia_)  # somme des distances au centroïde

# Tracer le graphique du ELbow
plt.figure(figsize=(8, 7))
plt.plot(K_range, inertia, marker='o')
plt.title('Méthode du coude pour trouver le meilleur k')
plt.xlabel('Nombre de clusters (k)')
plt.ylabel('Inertie')
plt.grid(True)
plt.show()

#Methode silhouette 
from sklearn.metrics import silhouette_score

scores = []
K_range = range(2, 6)  # silhouette commence à 2

for k in K_range:
    kmeans = KMeans(n_clusters=k, random_state=0)
    labels = kmeans.fit_predict(df[['x', 'y']])
    score = silhouette_score(df[['x', 'y']], labels)
    scores.append(score)
# Plot
plt.figure(figsize=(8, 6))
plt.plot(K_range, scores, marker='o', color='orange')
plt.title('Score de silhouette pour chaque k')
plt.xlabel('Nombre de clusters (k)')
plt.ylabel('Silhouette score')
plt.grid(True)
plt.show()

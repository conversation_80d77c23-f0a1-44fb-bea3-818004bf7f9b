# -*- coding: utf-8 -*-
"""
Created on Sun Apr  6 21:53:12 2025

@author: person
"""

import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import PCA
from sklearn.metrics.pairwise import cosine_similarity

# Sample DataFrame with text data
data = {'text': [  "I eat fish wich is good",
 "fish are good for our health",
 "We must take care of our health by eating fish, therefore, fish are good",
 "     good fish"  ]
    }
df = pd.DataFrame(data)

# Initialize the TfidfVectorizer
vectorizer = TfidfVectorizer(stop_words='english')

# Convert the text to a TF-IDF matrix
tfidf_matrix = vectorizer.fit_transform(df['text'])

# Calculate the cosine similarity between all documents
cosine_sim = cosine_similarity(tfidf_matrix)


 

# Apply PCA to reduce the dimensionality to 2D for plotting
pca = PCA(n_components=2)
reduced_vectors = pca.fit_transform(tfidf_matrix.toarray())

# Plotting the vectors
plt.figure(figsize=(8, 6))
sns.set(style="whitegrid")

# Plot each document as a point
for i in range(len(df)):
    plt.quiver(0, 0, reduced_vectors[i, 0], reduced_vectors[i, 1], angles='xy', scale_units='xy', scale=1, label=f'Document {i+1}')

# Annotate the points with document labels
for i in range(len(df)):
    plt.text(reduced_vectors[i, 0] + 0.05, reduced_vectors[i, 1] + 0.05, f'Doc {i+1}', fontsize=12)

# Set the plot limits
plt.xlim(-1, 1)
plt.ylim(-1, 1)

# Title and labels
plt.title('Cosine Similarity as Vectors (PCA Reduced)', fontsize=14)
plt.xlabel('PCA Component 1', fontsize=12)
plt.ylabel('PCA Component 2', fontsize=12)

# Show the plot
plt.legend(loc='upper left')
plt.show()

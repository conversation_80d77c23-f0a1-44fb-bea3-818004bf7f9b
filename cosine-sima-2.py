# -*- coding: utf-8 -*-
"""
Created on Sun Apr  6 21:49:20 2025

@author: person
"""

import pandas as pd
import seaborn as sns
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt

# Sample DataFrame with text data
data = {'text': [  "I eat fish wich is good",
 "fish are good for our health",
 "We must take care of our health by eating fish, therefore, fish are good",
 "     good fish"  ]
    }

df = pd.DataFrame(data)

# Initialize the TfidfVectorizer
vectorizer = TfidfVectorizer(stop_words='english')

# Convert the text to a TF-IDF matrix
tfidf_matrix = vectorizer.fit_transform(df['text'])

# Calculate cosine similarity between the first document and all other documents
cosine_sim = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix)

# Convert the cosine similarity result into a DataFrame for better readability
cosine_sim_df = pd.DataFrame(cosine_sim.T, columns=["similarity"], index=df.index)

# Display the cosine similarity scores
print(cosine_sim_df)
# Plotting the cosine similarity matrix as a heatmap
plt.figure(figsize=(8, 6))
sns.heatmap(cosine_sim_df, annot=True, cmap='coolwarm', cbar=True, xticklabels=df.index + 1, yticklabels=df.index + 1)

# Titles and labels
plt.title('Cosine Similarity Heatmap', fontsize=14)
plt.xlabel('Document Index', fontsize=12)
plt.ylabel('Document Index', fontsize=12)
plt.show()
